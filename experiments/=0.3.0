Requirement already satisfied: colpali-engine in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (0.3.12)
Requirement already satisfied: numpy in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from colpali-engine) (1.26.4)
Requirement already satisfied: peft<0.17.0,>=0.14.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from colpali-engine) (0.16.0)
Requirement already satisfied: pillow>=10.0.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from colpali-engine) (11.0.0)
Requirement already satisfied: requests in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from colpali-engine) (2.32.3)
Requirement already satisfied: scipy in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from colpali-engine) (1.13.1)
Requirement already satisfied: torch<2.8.0,>=2.5.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from colpali-engine) (2.7.1)
Requirement already satisfied: torchvision in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from colpali-engine) (0.22.1)
Requirement already satisfied: transformers<4.54.0,>=4.53.1 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from colpali-engine) (4.53.3)
Requirement already satisfied: packaging>=20.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from peft<0.17.0,>=0.14.0->colpali-engine) (24.0)
Requirement already satisfied: psutil in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from peft<0.17.0,>=0.14.0->colpali-engine) (5.9.8)
Requirement already satisfied: pyyaml in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from peft<0.17.0,>=0.14.0->colpali-engine) (6.0.1)
Requirement already satisfied: tqdm in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from peft<0.17.0,>=0.14.0->colpali-engine) (4.66.4)
Requirement already satisfied: accelerate>=0.21.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from peft<0.17.0,>=0.14.0->colpali-engine) (1.7.0)
Requirement already satisfied: safetensors in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from peft<0.17.0,>=0.14.0->colpali-engine) (0.4.3)
Requirement already satisfied: huggingface_hub>=0.25.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from peft<0.17.0,>=0.14.0->colpali-engine) (0.33.0)
Requirement already satisfied: filelock in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch<2.8.0,>=2.5.0->colpali-engine) (3.17.0)
Requirement already satisfied: typing-extensions>=4.10.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch<2.8.0,>=2.5.0->colpali-engine) (4.12.2)
Requirement already satisfied: sympy>=1.13.3 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch<2.8.0,>=2.5.0->colpali-engine) (1.14.0)
Requirement already satisfied: networkx in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch<2.8.0,>=2.5.0->colpali-engine) (3.2.1)
Requirement already satisfied: jinja2 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch<2.8.0,>=2.5.0->colpali-engine) (3.1.5)
Requirement already satisfied: fsspec in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch<2.8.0,>=2.5.0->colpali-engine) (2024.3.1)
Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.6.77 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch<2.8.0,>=2.5.0->colpali-engine) (12.6.77)
Requirement already satisfied: nvidia-cuda-runtime-cu12==12.6.77 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch<2.8.0,>=2.5.0->colpali-engine) (12.6.77)
Requirement already satisfied: nvidia-cuda-cupti-cu12==12.6.80 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch<2.8.0,>=2.5.0->colpali-engine) (12.6.80)
Requirement already satisfied: nvidia-cudnn-cu12==9.5.1.17 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch<2.8.0,>=2.5.0->colpali-engine) (9.5.1.17)
Requirement already satisfied: nvidia-cublas-cu12==12.6.4.1 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch<2.8.0,>=2.5.0->colpali-engine) (12.6.4.1)
Requirement already satisfied: nvidia-cufft-cu12==11.3.0.4 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch<2.8.0,>=2.5.0->colpali-engine) (11.3.0.4)
Requirement already satisfied: nvidia-curand-cu12==10.3.7.77 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch<2.8.0,>=2.5.0->colpali-engine) (10.3.7.77)
Requirement already satisfied: nvidia-cusolver-cu12==11.7.1.2 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch<2.8.0,>=2.5.0->colpali-engine) (11.7.1.2)
Requirement already satisfied: nvidia-cusparse-cu12==12.5.4.2 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch<2.8.0,>=2.5.0->colpali-engine) (12.5.4.2)
Requirement already satisfied: nvidia-cusparselt-cu12==0.6.3 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch<2.8.0,>=2.5.0->colpali-engine) (0.6.3)
Requirement already satisfied: nvidia-nccl-cu12==2.26.2 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch<2.8.0,>=2.5.0->colpali-engine) (2.26.2)
Requirement already satisfied: nvidia-nvtx-cu12==12.6.77 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch<2.8.0,>=2.5.0->colpali-engine) (12.6.77)
Requirement already satisfied: nvidia-nvjitlink-cu12==12.6.85 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch<2.8.0,>=2.5.0->colpali-engine) (12.6.85)
Requirement already satisfied: nvidia-cufile-cu12==1.11.1.6 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch<2.8.0,>=2.5.0->colpali-engine) (1.11.1.6)
Requirement already satisfied: triton==3.3.1 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from torch<2.8.0,>=2.5.0->colpali-engine) (3.3.1)
Requirement already satisfied: setuptools>=40.8.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from triton==3.3.1->torch<2.8.0,>=2.5.0->colpali-engine) (68.2.2)
Requirement already satisfied: regex!=2019.12.17 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from transformers<4.54.0,>=4.53.1->colpali-engine) (2024.4.28)
Requirement already satisfied: tokenizers<0.22,>=0.21 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from transformers<4.54.0,>=4.53.1->colpali-engine) (0.21.4)
Requirement already satisfied: hf-xet<2.0.0,>=1.1.2 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from huggingface_hub>=0.25.0->peft<0.17.0,>=0.14.0->colpali-engine) (1.1.3)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from sympy>=1.13.3->torch<2.8.0,>=2.5.0->colpali-engine) (1.3.0)
Requirement already satisfied: MarkupSafe>=2.0 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from jinja2->torch<2.8.0,>=2.5.0->colpali-engine) (2.1.5)
Requirement already satisfied: charset-normalizer<4,>=2 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from requests->colpali-engine) (3.3.2)
Requirement already satisfied: idna<4,>=2.5 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from requests->colpali-engine) (3.7)
Requirement already satisfied: urllib3<3,>=1.21.1 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from requests->colpali-engine) (2.2.1)
Requirement already satisfied: certifi>=2017.4.17 in /mnt/raid6/gjshim/miniconda3/envs/revise/lib/python3.9/site-packages (from requests->colpali-engine) (2024.2.2)
