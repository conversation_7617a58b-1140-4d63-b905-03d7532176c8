# !pip install -q byaldi langchain langchain_community langchain_anthropic pypdf langchain-chroma langchain_openai

# !pip install poppler-utils

import base64
import os
from dotenv import load_dotenv

load_dotenv()

print(os.environ.get('HF_TOKEN'))

os.environ["HF_TOKEN"]

from byaldi import RAGMultiModalModel

RAG = RAGMultiModalModel.from_pretrained("vidore/colpali-v1.2", verbose=1)

RAG.index(
    input_path="experiments/data/[이슈리포트 2022-2호] 혁신성장 정책금융 동향 (1).pdf",
    index_name="test_index",
    store_collection_with_index=True,
    overwrite=True,
)

