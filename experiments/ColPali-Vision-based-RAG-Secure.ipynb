{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ColPali Vision-based RAG - Secure Version\n", "\n", "This notebook demonstrates how to use ColPali for vision-based RAG with secure API key management.\n", "\n", "## Setup\n", "\n", "Before running this notebook:\n", "1. <PERSON><PERSON> the `.env.example` file to `.env` in the project root\n", "2. Fill in your actual API keys in the `.env` file\n", "3. Never commit the `.env` file to version control"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install -q byaldi langchain langchain_community langchain_anthropic pypdf langchain-chroma langchain_openai python-dotenv"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load API keys securely from .env file\n", "import sys\n", "import os\n", "\n", "# Add the project root to Python path to import config\n", "project_root = os.path.abspath('..')\n", "if project_root not in sys.path:\n", "    sys.path.append(project_root)\n", "\n", "# Import and setup API configuration\n", "from config.api_config import setup_api_keys\n", "\n", "# Load API keys from .env file\n", "api_keys = setup_api_keys()\n", "print(\"Loaded API keys:\", list(api_keys.keys()))\n", "\n", "# Verify that environment variables are set\n", "print(\"\\nEnvironment variables check:\")\n", "print(f\"HF_TOKEN: {'✓ Set' if os.environ.get('HF_TOKEN') else '✗ Not set'}\")\n", "print(f\"OPENAI_API_KEY: {'✓ Set' if os.environ.get('OPENAI_API_KEY') else '✗ Not set'}\")\n", "print(f\"ANTHROPIC_API_KEY: {'✓ Set' if os.environ.get('ANTHROPIC_API_KEY') else '✗ Not set'}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Now you can safely import and use libraries that require API keys\n", "import base64\n", "from byaldi import RAGMultiModalModel\n", "# Other imports as needed..."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Your ColPali RAG implementation goes here\n", "# Example:\n", "# model = RAGMultiModalModel.from_pretrained(\"vidore/colpali\")"]}], "metadata": {"kernelspec": {"display_name": "revise", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 2}