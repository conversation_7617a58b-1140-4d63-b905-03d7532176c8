"""
Example code to load API keys from .env file
Copy this code into your Jupyter notebook cells
"""

# Cell 1: Load API keys from .env file
import os
import sys
from pathlib import Path

# Method 1: Simple approach using python-dotenv (recommended)
try:
    from dotenv import load_dotenv
    
    # Load .env file from project root
    env_path = Path(__file__).parent / '.env'
    load_dotenv(env_path)
    
    print("✓ Loaded .env file using python-dotenv")
    
except ImportError:
    print("Installing python-dotenv...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "python-dotenv"])
    from dotenv import load_dotenv
    
    env_path = Path(__file__).parent / '.env'
    load_dotenv(env_path)
    print("✓ Installed and loaded python-dotenv")

# Method 2: Manual loading (if you prefer not to use python-dotenv)
def load_env_manual():
    env_path = Path(__file__).parent / '.env'
    if env_path.exists():
        with open(env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
        print("✓ Manually loaded .env file")
    else:
        print("✗ .env file not found")

# Verify API keys are loaded
print("\nAPI Keys Status:")
print(f"HF_TOKEN: {'✓ Set' if os.environ.get('HF_TOKEN') else '✗ Not set'}")
print(f"OPENAI_API_KEY: {'✓ Set' if os.environ.get('OPENAI_API_KEY') else '✗ Not set'}")
print(f"ANTHROPIC_API_KEY: {'✓ Set' if os.environ.get('ANTHROPIC_API_KEY') else '✗ Not set'}")

# Cell 2: Now you can use the APIs
import base64
from byaldi import RAGMultiModalModel

# The HF_TOKEN is now automatically available to byaldi and other HuggingFace libraries
# You can also access it directly if needed:
hf_token = os.environ.get('HF_TOKEN')
print(f"HF Token (first 10 chars): {hf_token[:10]}..." if hf_token else "HF Token not found")
