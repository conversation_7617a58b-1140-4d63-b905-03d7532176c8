"""
API Configuration Module

This module handles loading API keys and tokens from environment variables
or a .env file for secure credential management.
"""

import os
from pathlib import Path
from typing import Optional

try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False
    print("Warning: python-dotenv not installed. Install it with: pip install python-dotenv")

def load_env_file(env_path: Optional[str] = None) -> None:
    """
    Load environment variables from a .env file.
    
    Args:
        env_path: Path to the .env file. If None, looks for .env in the project root.
    """
    if env_path is None:
        # Look for .env file in the project root
        project_root = Path(__file__).parent.parent
        env_path = project_root / ".env"
    
    env_file = Path(env_path)
    if not env_file.exists():
        print(f"Warning: .env file not found at {env_file}")
        return
    
    with open(env_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()
                # Only set if not already in environment
                if key not in os.environ:
                    os.environ[key] = value

def get_api_key(key_name: str, required: bool = True) -> Optional[str]:
    """
    Get an API key from environment variables.
    
    Args:
        key_name: Name of the environment variable
        required: Whether the key is required (raises error if missing)
    
    Returns:
        The API key value or None if not found and not required
    
    Raises:
        ValueError: If the key is required but not found
    """
    value = os.environ.get(key_name)
    if required and not value:
        raise ValueError(
            f"Required API key '{key_name}' not found in environment variables. "
            f"Please set it in your .env file or environment."
        )
    return value

def setup_api_keys() -> dict:
    """
    Load and setup all API keys.
    
    Returns:
        Dictionary containing all loaded API keys
    """
    # Load .env file first
    load_env_file()
    
    # Get API keys
    api_keys = {}
    
    # Hugging Face Token
    try:
        api_keys['HF_TOKEN'] = get_api_key('HF_TOKEN', required=False)
        if api_keys['HF_TOKEN']:
            os.environ['HF_TOKEN'] = api_keys['HF_TOKEN']
    except ValueError as e:
        print(f"Warning: {e}")
    
    # OpenAI API Key
    try:
        api_keys['OPENAI_API_KEY'] = get_api_key('OPENAI_API_KEY', required=False)
        if api_keys['OPENAI_API_KEY']:
            os.environ['OPENAI_API_KEY'] = api_keys['OPENAI_API_KEY']
    except ValueError as e:
        print(f"Warning: {e}")
    
    # Anthropic API Key
    try:
        api_keys['ANTHROPIC_API_KEY'] = get_api_key('ANTHROPIC_API_KEY', required=False)
        if api_keys['ANTHROPIC_API_KEY']:
            os.environ['ANTHROPIC_API_KEY'] = api_keys['ANTHROPIC_API_KEY']
    except ValueError as e:
        print(f"Warning: {e}")
    
    return {k: v for k, v in api_keys.items() if v is not None}

# Auto-setup when module is imported
if __name__ != "__main__":
    setup_api_keys()
